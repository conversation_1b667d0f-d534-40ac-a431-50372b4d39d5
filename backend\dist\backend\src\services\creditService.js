"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.creditService = exports.CreditError = exports.CreditService = void 0;
const supabaseService_1 = require("./supabaseService");
class CreditService {
    // Get current AI operation cost
    async getOperationCost(operationType) {
        const { data, error } = await supabaseService_1.supabase
            .from('ai_operation_costs')
            .select('credits_required')
            .eq('operation_type', operationType)
            .eq('is_active', true)
            .single();
        if (error || !data) {
            throw new Error(`Invalid operation type: ${operationType}`);
        }
        return data.credits_required;
    }
    // Get all active operation costs
    async getAllOperationCosts() {
        const { data, error } = await supabaseService_1.supabase
            .from('ai_operation_costs')
            .select('*')
            .eq('is_active', true)
            .order('operation_type');
        if (error) {
            throw new Error(`Failed to get operation costs: ${error.message}`);
        }
        return data || [];
    }
    // Atomic credit deduction using stored procedure
    async deductCredits(userId, operationType, metadata, studySetId) {
        // Use the existing use_credits function which automatically determines cost
        const { data, error } = await supabaseService_1.supabase.rpc('use_credits', {
            p_user_id: userId,
            p_operation_type: operationType,
            p_description: `AI generation: ${operationType}`,
            p_study_set_id: studySetId || null,
            p_metadata: metadata || {},
            p_ip_address: null,
            p_user_agent: null
        });
        if (error) {
            throw new CreditError('Credit deduction failed', error);
        }
        // The use_credits function returns a boolean indicating success
        // We need to get the remaining credits separately if the operation succeeded
        if (data === true) {
            const remainingCredits = await this.getUserCredits(userId);
            return {
                success: true,
                remainingCredits,
                message: 'Credits deducted successfully'
            };
        }
        else {
            return {
                success: false,
                remainingCredits: await this.getUserCredits(userId),
                message: 'Insufficient credits'
            };
        }
    }
    // Add credits for purchases/subscriptions
    async addCredits(userId, creditsToAdd, source, referenceId) {
        const { data, error } = await supabaseService_1.supabase.rpc('add_credits', {
            p_user_id: userId,
            p_credits_to_add: creditsToAdd,
            p_operation_type: source,
            p_description: `Credit addition: ${source}${referenceId ? ` (${referenceId})` : ''}`,
            p_metadata: referenceId ? { reference_id: referenceId } : {},
            p_ip_address: null,
            p_user_agent: null
        });
        if (error) {
            throw new CreditError('Credit addition failed', error);
        }
        // The add_credits function returns a boolean indicating success
        // We need to get the new balance separately if the operation succeeded
        if (data === true) {
            const newBalance = await this.getUserCredits(userId);
            return {
                success: true,
                newBalance,
                message: 'Credits added successfully'
            };
        }
        else {
            return {
                success: false,
                newBalance: await this.getUserCredits(userId),
                message: 'Failed to add credits'
            };
        }
    }
    // Get user's current credit balance
    async getUserCredits(userId) {
        const { data, error } = await supabaseService_1.supabase
            .from('users')
            .select('credits_remaining')
            .eq('id', userId)
            .single();
        if (error || !data) {
            throw new Error('User not found');
        }
        return data.credits_remaining;
    }
    // Validate credit operation before execution
    async validateCreditOperation(userId, operationType) {
        const [userCredits, requiredCredits] = await Promise.all([
            this.getUserCredits(userId),
            this.getOperationCost(operationType)
        ]);
        return userCredits >= requiredCredits;
    }
    // Get user's credit transaction history
    async getCreditHistory(userId, limit = 50, offset = 0) {
        // Get transactions
        const { data: transactions, error: transError } = await supabaseService_1.supabase
            .from('credit_transactions')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false })
            .range(offset, offset + limit - 1);
        if (transError) {
            throw new Error(`Failed to get credit history: ${transError.message}`);
        }
        // Get total count
        const { count, error: countError } = await supabaseService_1.supabase
            .from('credit_transactions')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', userId);
        if (countError) {
            throw new Error(`Failed to get credit history count: ${countError.message}`);
        }
        return {
            transactions: transactions || [],
            total: count || 0
        };
    }
    // Get credit usage statistics
    async getCreditStats(userId, days = 30) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const { data, error } = await supabaseService_1.supabase
            .from('credit_transactions')
            .select('credits_used, operation_type')
            .eq('user_id', userId)
            .gte('created_at', startDate.toISOString());
        if (error) {
            throw new Error(`Failed to get credit stats: ${error.message}`);
        }
        const transactions = data || [];
        const totalUsed = transactions
            .filter(t => t.credits_used > 0)
            .reduce((sum, t) => sum + t.credits_used, 0);
        const totalAdded = Math.abs(transactions
            .filter(t => t.credits_used < 0)
            .reduce((sum, t) => sum + t.credits_used, 0));
        // Group by operation type
        const operationMap = new Map();
        transactions
            .filter(t => t.credits_used > 0)
            .forEach(t => {
            const existing = operationMap.get(t.operation_type) || { credits_used: 0, count: 0 };
            operationMap.set(t.operation_type, {
                credits_used: existing.credits_used + t.credits_used,
                count: existing.count + 1
            });
        });
        const operationBreakdown = Array.from(operationMap.entries()).map(([operation_type, stats]) => ({
            operation_type,
            ...stats
        }));
        return {
            totalUsed,
            totalAdded,
            operationBreakdown
        };
    }
    // Check if user has sufficient credits for operation
    async checkSufficientCredits(userId, operationType) {
        const [currentCredits, requiredCredits] = await Promise.all([
            this.getUserCredits(userId),
            this.getOperationCost(operationType)
        ]);
        const sufficient = currentCredits >= requiredCredits;
        const shortfall = sufficient ? 0 : requiredCredits - currentCredits;
        return {
            sufficient,
            currentCredits,
            requiredCredits,
            shortfall
        };
    }
}
exports.CreditService = CreditService;
// Custom error class for credit operations
class CreditError extends Error {
    constructor(message, originalError) {
        super(message);
        this.originalError = originalError;
        this.name = 'CreditError';
    }
}
exports.CreditError = CreditError;
exports.creditService = new CreditService();
//# sourceMappingURL=creditService.js.map