import { Router, Request, Response } from "express";
import { supabase, supabaseService } from "../services/supabaseService";
import { authenticateToken } from "../middleware/auth";
import { AuthResult } from "../../../shared/types";

const router = Router();

router.post("/signup", async (req: Request, res: Response) => {
  try {
    const { email, password, name } = req.body;

    if (!email || !password) {
      return res
        .status(400)
        .json({ success: false, error: "Email and password are required" });
    }

    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: { data: { name } },
    });

    if (authError) {
      return res.status(400).json({ success: false, error: authError.message });
    }

    if (!authData.user) {
      return res
        .status(400)
        .json({ success: false, error: "Failed to create user account" });
    }

    const profile = await supabaseService.createUserProfile(
      authData.user.id,
      email,
      name
    );

    const result: AuthResult = {
      success: true,
      user: profile,
      token: authData.session?.access_token,
    };

    res.status(201).json(result);
  } catch (error) {
    console.error('Signup error:', error);
    res.status(500).json({ success: false, error: "Internal server error" });
  }
});

router.post("/login", async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res
        .status(400)
        .json({ success: false, error: "Email and password are required" });
    }

    const { data: authData, error: authError } =
      await supabase.auth.signInWithPassword({
        email,
        password,
      });

    if (authError) {
      return res.status(401).json({ success: false, error: authError.message });
    }

    if (!authData.user || !authData.session) {
      return res
        .status(401)
        .json({ success: false, error: "Authentication failed" });
    }

    const profile = await supabaseService.getUserProfile(authData.user.id);

    if (!profile) {
      return res
        .status(404)
        .json({ success: false, error: "User profile not found" });
    }

    await supabaseService.updateUserProfile(authData.user.id, {
      last_login: new Date().toISOString(),
    });

    const result: AuthResult = {
      success: true,
      user: profile,
      token: authData.session.access_token,
    };

    res.json(result);
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, error: "Internal server error" });
  }
});

router.get("/user", authenticateToken, async (req: Request, res: Response) => {
  try {
    const profile = await supabaseService.getUserProfile(req.user!.id);
    res.json({ success: true, data: profile });
  } catch (error) {
    console.error('Get user error:', error);
    res
      .status(500)
      .json({ success: false, error: "Failed to get user profile" });
  }
});

// Debug endpoint to check token
router.post("/debug-token", async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];

    console.log("Debug - Auth header:", authHeader);
    console.log("Debug - Token:", token ? `${token.substring(0, 20)}...` : "null");

    if (!token) {
      return res.json({
        success: false,
        error: "No token provided",
        authHeader: authHeader || "missing"
      });
    }

    const { data: { user }, error } = await supabaseService.verifyToken(token);

    console.log("Debug - Supabase verification error:", error);
    console.log("Debug - Supabase user:", user ? user.id : "null");

    if (error || !user) {
      return res.json({
        success: false,
        error: "Token verification failed",
        supabaseError: error?.message || "Unknown error"
      });
    }

    const profile = await supabaseService.getUserProfile(user.id);

    console.log("Debug - Profile found:", !!profile);
    console.log("Debug - Profile active:", profile?.is_active);

    res.json({
      success: true,
      data: {
        userId: user.id,
        email: user.email,
        profileExists: !!profile,
        profileActive: profile?.is_active
      }
    });
  } catch (error: any) {
    console.log("Debug - Exception:", error.message);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post(
  "/logout",
  authenticateToken,
  async (_req: Request, res: Response) => {
    try {
      // Supabase handles token invalidation on client side
      res.json({ success: true, message: "Logged out successfully" });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        error: 'Logout failed'
      });
    }
  }
);

export default router;
