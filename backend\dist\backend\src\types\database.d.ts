export type Json = string | number | boolean | null | {
    [key: string]: Json | undefined;
} | Json[];
export type Database = {
    public: {
        Tables: {
            ai_operation_costs: {
                Row: {
                    created_at: string | null;
                    credits_required: number;
                    description: string | null;
                    id: string;
                    is_active: boolean | null;
                    operation_type: string;
                    updated_at: string | null;
                };
                Insert: {
                    created_at?: string | null;
                    credits_required: number;
                    description?: string | null;
                    id?: string;
                    is_active?: boolean | null;
                    operation_type: string;
                    updated_at?: string | null;
                };
                Update: {
                    created_at?: string | null;
                    credits_required?: number;
                    description?: string | null;
                    id?: string;
                    is_active?: boolean | null;
                    operation_type?: string;
                    updated_at?: string | null;
                };
                Relationships: [];
            };
            credit_transactions: {
                Row: {
                    created_at: string | null;
                    credits_used: number;
                    description: string;
                    id: string;
                    ip_address: unknown | null;
                    metadata: Json | null;
                    operation_type: string;
                    study_set_id: string | null;
                    user_agent: string | null;
                    user_id: string;
                };
                Insert: {
                    created_at?: string | null;
                    credits_used: number;
                    description: string;
                    id?: string;
                    ip_address?: unknown | null;
                    metadata?: Json | null;
                    operation_type: string;
                    study_set_id?: string | null;
                    user_agent?: string | null;
                    user_id: string;
                };
                Update: {
                    created_at?: string | null;
                    credits_used?: number;
                    description?: string;
                    id?: string;
                    ip_address?: unknown | null;
                    metadata?: Json | null;
                    operation_type?: string;
                    study_set_id?: string | null;
                    user_agent?: string | null;
                    user_id?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "credit_transactions_study_set_id_fkey";
                        columns: ["study_set_id"];
                        isOneToOne: false;
                        referencedRelation: "study_sets";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "credit_transactions_user_id_fkey";
                        columns: ["user_id"];
                        isOneToOne: false;
                        referencedRelation: "users";
                        referencedColumns: ["id"];
                    }
                ];
            };
            documents: {
                Row: {
                    content_text: string | null;
                    file_size: number;
                    file_type: string;
                    filename: string;
                    id: string;
                    is_processed: boolean | null;
                    processing_error: string | null;
                    supabase_storage_path: string;
                    uploaded_at: string | null;
                    user_id: string;
                };
                Insert: {
                    content_text?: string | null;
                    file_size: number;
                    file_type: string;
                    filename: string;
                    id?: string;
                    is_processed?: boolean | null;
                    processing_error?: string | null;
                    supabase_storage_path: string;
                    uploaded_at?: string | null;
                    user_id: string;
                };
                Update: {
                    content_text?: string | null;
                    file_size?: number;
                    file_type?: string;
                    filename?: string;
                    id?: string;
                    is_processed?: boolean | null;
                    processing_error?: string | null;
                    supabase_storage_path?: string;
                    uploaded_at?: string | null;
                    user_id?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "documents_user_id_fkey";
                        columns: ["user_id"];
                        isOneToOne: false;
                        referencedRelation: "users";
                        referencedColumns: ["id"];
                    }
                ];
            };
            flashcards: {
                Row: {
                    back: string;
                    created_at: string | null;
                    difficulty_level: number | null;
                    front: string;
                    id: string;
                    is_ai_generated: boolean | null;
                    is_flagged: boolean | null;
                    last_reviewed_at: string | null;
                    study_set_id: string;
                    times_reviewed: number | null;
                };
                Insert: {
                    back: string;
                    created_at?: string | null;
                    difficulty_level?: number | null;
                    front: string;
                    id?: string;
                    is_ai_generated?: boolean | null;
                    is_flagged?: boolean | null;
                    last_reviewed_at?: string | null;
                    study_set_id: string;
                    times_reviewed?: number | null;
                };
                Update: {
                    back?: string;
                    created_at?: string | null;
                    difficulty_level?: number | null;
                    front?: string;
                    id?: string;
                    is_ai_generated?: boolean | null;
                    is_flagged?: boolean | null;
                    last_reviewed_at?: string | null;
                    study_set_id?: string;
                    times_reviewed?: number | null;
                };
                Relationships: [
                    {
                        foreignKeyName: "flashcards_study_set_id_fkey";
                        columns: ["study_set_id"];
                        isOneToOne: false;
                        referencedRelation: "study_sets";
                        referencedColumns: ["id"];
                    }
                ];
            };
            quiz_questions: {
                Row: {
                    correct_answers: Json;
                    created_at: string | null;
                    difficulty_level: number | null;
                    explanation: string | null;
                    id: string;
                    is_ai_generated: boolean | null;
                    options: Json | null;
                    question_text: string;
                    question_type: string;
                    study_set_id: string;
                    times_attempted: number | null;
                    times_correct: number | null;
                };
                Insert: {
                    correct_answers: Json;
                    created_at?: string | null;
                    difficulty_level?: number | null;
                    explanation?: string | null;
                    id?: string;
                    is_ai_generated?: boolean | null;
                    options?: Json | null;
                    question_text: string;
                    question_type: string;
                    study_set_id: string;
                    times_attempted?: number | null;
                    times_correct?: number | null;
                };
                Update: {
                    correct_answers?: Json;
                    created_at?: string | null;
                    difficulty_level?: number | null;
                    explanation?: string | null;
                    id?: string;
                    is_ai_generated?: boolean | null;
                    options?: Json | null;
                    question_text?: string;
                    question_type?: string;
                    study_set_id?: string;
                    times_attempted?: number | null;
                    times_correct?: number | null;
                };
                Relationships: [
                    {
                        foreignKeyName: "quiz_questions_study_set_id_fkey";
                        columns: ["study_set_id"];
                        isOneToOne: false;
                        referencedRelation: "study_sets";
                        referencedColumns: ["id"];
                    }
                ];
            };
            study_sets: {
                Row: {
                    created_at: string | null;
                    custom_prompt: string | null;
                    id: string;
                    is_ai_generated: boolean | null;
                    last_studied_at: string | null;
                    name: string;
                    source_documents: Json | null;
                    total_items: number | null;
                    type: string;
                    updated_at: string | null;
                    user_id: string;
                };
                Insert: {
                    created_at?: string | null;
                    custom_prompt?: string | null;
                    id?: string;
                    is_ai_generated?: boolean | null;
                    last_studied_at?: string | null;
                    name: string;
                    source_documents?: Json | null;
                    total_items?: number | null;
                    type: string;
                    updated_at?: string | null;
                    user_id: string;
                };
                Update: {
                    created_at?: string | null;
                    custom_prompt?: string | null;
                    id?: string;
                    is_ai_generated?: boolean | null;
                    last_studied_at?: string | null;
                    name?: string;
                    source_documents?: Json | null;
                    total_items?: number | null;
                    type?: string;
                    updated_at?: string | null;
                    user_id?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "study_sets_user_id_fkey";
                        columns: ["user_id"];
                        isOneToOne: false;
                        referencedRelation: "users";
                        referencedColumns: ["id"];
                    }
                ];
            };
            users: {
                Row: {
                    created_at: string | null;
                    credits_remaining: number | null;
                    email: string;
                    id: string;
                    is_active: boolean | null;
                    last_login: string | null;
                    name: string | null;
                    stripe_customer_id: string | null;
                    subscription_expires_at: string | null;
                    subscription_tier: string | null;
                    updated_at: string | null;
                };
                Insert: {
                    created_at?: string | null;
                    credits_remaining?: number | null;
                    email: string;
                    id: string;
                    is_active?: boolean | null;
                    last_login?: string | null;
                    name?: string | null;
                    stripe_customer_id?: string | null;
                    subscription_expires_at?: string | null;
                    subscription_tier?: string | null;
                    updated_at?: string | null;
                };
                Update: {
                    created_at?: string | null;
                    credits_remaining?: number | null;
                    email?: string;
                    id?: string;
                    is_active?: boolean | null;
                    last_login?: string | null;
                    name?: string | null;
                    stripe_customer_id?: string | null;
                    subscription_expires_at?: string | null;
                    subscription_tier?: string | null;
                    updated_at?: string | null;
                };
                Relationships: [];
            };
        };
        Views: {
            [_ in never]: never;
        };
        Functions: {
            add_credits: {
                Args: {
                    p_user_id: string;
                    p_credits_to_add: number;
                    p_operation_type: string;
                    p_description: string;
                    p_metadata?: Json;
                    p_ip_address?: unknown;
                    p_user_agent?: string;
                };
                Returns: boolean;
            };
            use_credits: {
                Args: {
                    p_user_id: string;
                    p_operation_type: string;
                    p_description: string;
                    p_study_set_id?: string;
                    p_metadata?: Json;
                    p_ip_address?: unknown;
                    p_user_agent?: string;
                };
                Returns: boolean;
            };
        };
        Enums: {
            [_ in never]: never;
        };
        CompositeTypes: {
            [_ in never]: never;
        };
    };
};
type DefaultSchema = Database[Extract<keyof Database, "public">];
export type Tables<DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] & DefaultSchema["Views"]) | {
    schema: keyof Database;
}, TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
} ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] & Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"]) : never = never> = DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
} ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] & Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
    Row: infer R;
} ? R : never : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] & DefaultSchema["Views"]) ? (DefaultSchema["Tables"] & DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
    Row: infer R;
} ? R : never : never;
export type TablesInsert<DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"] | {
    schema: keyof Database;
}, TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
} ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] : never = never> = DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
} ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Insert: infer I;
} ? I : never : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"] ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Insert: infer I;
} ? I : never : never;
export type TablesUpdate<DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"] | {
    schema: keyof Database;
}, TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
} ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] : never = never> = DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
} ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Update: infer U;
} ? U : never : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"] ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Update: infer U;
} ? U : never : never;
export {};
//# sourceMappingURL=database.d.ts.map