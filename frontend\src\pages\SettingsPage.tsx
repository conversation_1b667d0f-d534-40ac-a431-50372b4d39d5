import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>eld<PERSON><PERSON><PERSON>, 
  HiCreditCard,
  HiMoon,
  <PERSON>Sun,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>rash
} from 'react-icons/hi';
import { Button } from '../components/common/Button';
import { Input } from '../components/common/Input';
import { useAuthStore } from '../stores/authStore';

interface SettingsSection {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const settingsSections: SettingsSection[] = [
  {
    id: 'profile',
    label: 'Profile',
    icon: HiUser,
    description: 'Manage your account information'
  },
  {
    id: 'preferences',
    label: 'Preferences',
    icon: HiCog,
    description: 'Customize your experience'
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: HiBell,
    description: 'Control notification settings'
  },
  {
    id: 'security',
    label: 'Security',
    icon: HiShieldCheck,
    description: 'Password and security settings'
  },
  {
    id: 'billing',
    label: 'Billing',
    icon: HiCreditCard,
    description: 'Manage subscription and payments'
  }
];

export const SettingsPage: React.FC = () => {
  const [activeSection, setActiveSection] = useState('profile');
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuthStore();

  // Form states
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    bio: ''
  });

  const [preferences, setPreferences] = useState({
    theme: 'dark',
    language: 'en',
    studyReminders: true,
    autoSave: true
  });

  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    studyReminders: true,
    weeklyProgress: false,
    marketingEmails: false
  });

  const handleSaveProfile = async () => {
    setIsLoading(true);
    // TODO: Implement profile update
    setTimeout(() => setIsLoading(false), 1000);
  };

  const handleSavePreferences = async () => {
    setIsLoading(true);
    // TODO: Implement preferences update
    setTimeout(() => setIsLoading(false), 1000);
  };

  const renderProfileSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Profile Information</h3>
        <div className="space-y-4">
          <Input
            label="Full Name"
            value={profileData.name}
            onChange={(value) => setProfileData({ ...profileData, name: value })}
            placeholder="Enter your full name"
          />
          <Input
            label="Email Address"
            type="email"
            value={profileData.email}
            onChange={(value) => setProfileData({ ...profileData, email: value })}
            placeholder="Enter your email"
            disabled
          />
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Bio (Optional)
            </label>
            <textarea
              value={profileData.bio}
              onChange={(e) => setProfileData({ ...profileData, bio: e.target.value })}
              placeholder="Tell us about yourself..."
              rows={4}
              className="w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>
        <div className="mt-6">
          <Button onClick={handleSaveProfile} isLoading={isLoading}>
            Save Profile
          </Button>
        </div>
      </div>
    </div>
  );

  const renderPreferencesSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">App Preferences</h3>
        <div className="space-y-6">
          {/* Theme Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">Theme</label>
            <div className="flex space-x-4">
              <button
                onClick={() => setPreferences({ ...preferences, theme: 'dark' })}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  preferences.theme === 'dark'
                    ? 'border-primary-500 bg-primary-500/20 text-primary-400'
                    : 'border-gray-600 text-gray-300 hover:border-gray-500'
                }`}
              >
                <HiMoon className="w-4 h-4" />
                <span>Dark</span>
              </button>
              <button
                onClick={() => setPreferences({ ...preferences, theme: 'light' })}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  preferences.theme === 'light'
                    ? 'border-primary-500 bg-primary-500/20 text-primary-400'
                    : 'border-gray-600 text-gray-300 hover:border-gray-500'
                }`}
                disabled
              >
                <HiSun className="w-4 h-4" />
                <span>Light (Coming Soon)</span>
              </button>
            </div>
          </div>

          {/* Language Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">Language</label>
            <select
              value={preferences.language}
              onChange={(e) => setPreferences({ ...preferences, language: e.target.value })}
              className="w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="en">English</option>
              <option value="es" disabled>Spanish (Coming Soon)</option>
              <option value="fr" disabled>French (Coming Soon)</option>
            </select>
          </div>

          {/* Toggle Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">Study Reminders</label>
                <p className="text-xs text-gray-500">Get reminded to study regularly</p>
              </div>
              <button
                onClick={() => setPreferences({ ...preferences, studyReminders: !preferences.studyReminders })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  preferences.studyReminders ? 'bg-primary-500' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    preferences.studyReminders ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">Auto-save</label>
                <p className="text-xs text-gray-500">Automatically save your progress</p>
              </div>
              <button
                onClick={() => setPreferences({ ...preferences, autoSave: !preferences.autoSave })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  preferences.autoSave ? 'bg-primary-500' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    preferences.autoSave ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>
        <div className="mt-6">
          <Button onClick={handleSavePreferences} isLoading={isLoading}>
            Save Preferences
          </Button>
        </div>
      </div>
    </div>
  );

  const renderNotificationsSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Notification Settings</h3>
        <div className="space-y-4">
          {Object.entries(notifications).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300 capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </label>
                <p className="text-xs text-gray-500">
                  {key === 'emailNotifications' && 'Receive important updates via email'}
                  {key === 'studyReminders' && 'Get reminded when it\'s time to study'}
                  {key === 'weeklyProgress' && 'Weekly summary of your study progress'}
                  {key === 'marketingEmails' && 'Product updates and tips'}
                </p>
              </div>
              <button
                onClick={() => setNotifications({ ...notifications, [key]: !value })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  value ? 'bg-primary-500' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    value ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSecuritySection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Security Settings</h3>
        <div className="space-y-6">
          <div className="bg-background-tertiary rounded-lg p-4 border border-border-primary">
            <div className="flex items-center space-x-3 mb-3">
              <HiKey className="w-5 h-5 text-primary-400" />
              <h4 className="font-medium text-white">Change Password</h4>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Update your password to keep your account secure
            </p>
            <Button variant="secondary">Change Password</Button>
          </div>

          <div className="bg-background-tertiary rounded-lg p-4 border border-border-primary">
            <div className="flex items-center space-x-3 mb-3">
              <HiTrash className="w-5 h-5 text-red-400" />
              <h4 className="font-medium text-white">Delete Account</h4>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Permanently delete your account and all associated data
            </p>
            <Button variant="danger">Delete Account</Button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderBillingSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Billing & Subscription</h3>
        <div className="bg-background-tertiary rounded-lg p-6 border border-border-primary">
          <div className="text-center">
            <HiCreditCard className="w-16 h-16 text-gray-500 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-white mb-2">Free Plan</h4>
            <p className="text-gray-400 mb-6">
              You're currently on the free plan with limited AI generations
            </p>
            <Button>Upgrade to Pro</Button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'profile':
        return renderProfileSection();
      case 'preferences':
        return renderPreferencesSection();
      case 'notifications':
        return renderNotificationsSection();
      case 'security':
        return renderSecuritySection();
      case 'billing':
        return renderBillingSection();
      default:
        return renderProfileSection();
    }
  };

  return (
    <div className="min-h-screen bg-background-primary text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Settings</h1>
          <p className="text-gray-400">Manage your account and preferences</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Settings Navigation */}
          <div className="lg:col-span-1">
            <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
              <nav className="space-y-2">
                {settingsSections.map((section) => {
                  const Icon = section.icon;
                  const isActive = activeSection === section.id;
                  
                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${isActive 
                          ? 'bg-primary-500/20 text-primary-400 border border-primary-500/30' 
                          : 'text-gray-300 hover:bg-background-tertiary hover:text-white'
                        }
                      `}
                    >
                      <Icon className="w-5 h-5" />
                      <div className="flex-1 min-w-0">
                        <span className="font-medium block">{section.label}</span>
                        <span className="text-xs text-gray-500 block truncate">
                          {section.description}
                        </span>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Settings Content */}
          <div className="lg:col-span-3">
            <motion.div
              key={activeSection}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-background-secondary rounded-lg p-6 border border-border-primary"
            >
              {renderContent()}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};
