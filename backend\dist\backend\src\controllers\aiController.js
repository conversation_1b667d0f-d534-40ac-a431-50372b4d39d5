"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStudySetContent = exports.getStudySets = exports.testAIConnection = exports.generateQuiz = exports.generateMoreQuizQuestions = exports.generateMoreFlashcards = exports.generateFlashcards = void 0;
const aiService_1 = require("../services/aiService");
const studySetService_1 = require("../services/studySetService");
const documentDbService_1 = require("../services/documentDbService");
const creditService_1 = require("../services/creditService");
const generateFlashcards = async (req, res) => {
    try {
        const userId = req.user.id;
        const { documentIds, name, count = 10, customPrompt } = req.body;
        // Validate input
        if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Document IDs are required'
            });
        }
        if (!name || name.trim().length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Study set name is required'
            });
        }
        // Get documents and combine content
        const documents = await documentDbService_1.documentDbService.getDocumentsByIds(documentIds, userId);
        if (documents.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'No documents found'
            });
        }
        const combinedContent = documents
            .map(doc => `${doc.filename}:\n${doc.content_text}`)
            .join('\n\n---\n\n');
        if (combinedContent.trim().length < 100) {
            return res.status(400).json({
                success: false,
                error: 'Document content is too short for AI generation'
            });
        }
        // Deduct credits
        const creditResult = await creditService_1.creditService.deductCredits(userId, 'flashcard_generation', { documentIds, count, customPrompt: !!customPrompt });
        if (!creditResult.success) {
            return res.status(402).json({
                success: false,
                error: creditResult.message
            });
        }
        try {
            // Generate flashcards with AI
            const flashcards = await aiService_1.aiService.generateFlashcards(combinedContent, count, customPrompt);
            // Create study set
            const studySet = await studySetService_1.studySetService.createStudySet({
                user_id: userId,
                name: name.trim(),
                type: 'flashcards',
                is_ai_generated: true,
                source_documents: documents.map(doc => ({ id: doc.id, filename: doc.filename })),
                custom_prompt: customPrompt || null
            });
            // Add flashcards to study set
            await studySetService_1.studySetService.addFlashcardsToSet(studySet.id, flashcards);
            res.status(201).json({
                success: true,
                data: {
                    studySet,
                    flashcards,
                    creditsRemaining: creditResult.remainingCredits
                },
                message: 'Flashcards generated successfully'
            });
        }
        catch (aiError) {
            // Refund credits if AI generation fails
            await creditService_1.creditService.addCredits(userId, 1, 'refund_failed_generation', `flashcard_generation_${Date.now()}`);
            throw aiError;
        }
    }
    catch (error) {
        console.error('Generate flashcards error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to generate flashcards'
        });
    }
};
exports.generateFlashcards = generateFlashcards;
const generateMoreFlashcards = async (req, res) => {
    try {
        const userId = req.user.id;
        const { studySetId, documentIds, count = 10, customPrompt } = req.body;
        // Validate input
        if (!studySetId) {
            return res.status(400).json({
                success: false,
                error: 'Study set ID is required'
            });
        }
        if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Document IDs are required'
            });
        }
        // Verify user owns the study set
        const studySet = await studySetService_1.studySetService.getStudySetById(studySetId, userId);
        if (!studySet) {
            return res.status(404).json({
                success: false,
                error: 'Study set not found or access denied'
            });
        }
        // Get documents and combine content
        const documents = await documentDbService_1.documentDbService.getDocumentsByIds(documentIds, userId);
        if (documents.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'No documents found'
            });
        }
        const combinedContent = documents
            .map(doc => `${doc.filename}:\n${doc.content_text}`)
            .join('\n\n---\n\n');
        if (combinedContent.trim().length < 100) {
            return res.status(400).json({
                success: false,
                error: 'Document content is too short for AI generation'
            });
        }
        // Deduct credits
        const creditResult = await creditService_1.creditService.deductCredits(userId, 'flashcard_generation', count);
        if (!creditResult.success) {
            return res.status(402).json({
                success: false,
                error: creditResult.message
            });
        }
        // Generate flashcards using AI
        const flashcards = await aiService_1.aiService.generateFlashcards(combinedContent, count, customPrompt);
        // Add flashcards to the existing study set
        const createdFlashcards = [];
        for (const flashcard of flashcards) {
            try {
                const created = await studySetService_1.studySetService.addFlashcardToSet(studySetId, {
                    front: flashcard.front,
                    back: flashcard.back,
                    difficulty_level: flashcard.difficulty_level || 3,
                    is_ai_generated: true,
                    times_reviewed: 0
                });
                createdFlashcards.push(created);
            }
            catch (error) {
                console.error('Error adding flashcard to set:', error);
                // Continue with other flashcards even if one fails
            }
        }
        res.json({
            success: true,
            data: {
                flashcards: createdFlashcards,
                creditsRemaining: creditResult.remainingCredits
            }
        });
    }
    catch (error) {
        console.error('Generate more flashcards error:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to generate flashcards'
        });
    }
};
exports.generateMoreFlashcards = generateMoreFlashcards;
const generateMoreQuizQuestions = async (req, res) => {
    try {
        const userId = req.user.id;
        const { studySetId, documentIds, count = 10, customPrompt } = req.body;
        // Validate input
        if (!studySetId) {
            return res.status(400).json({
                success: false,
                error: 'Study set ID is required'
            });
        }
        if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Document IDs are required'
            });
        }
        // Verify user owns the study set
        const studySet = await studySetService_1.studySetService.getStudySetById(studySetId, userId);
        if (!studySet) {
            return res.status(404).json({
                success: false,
                error: 'Study set not found or access denied'
            });
        }
        // Get documents and combine content
        const documents = await documentDbService_1.documentDbService.getDocumentsByIds(documentIds, userId);
        if (documents.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'No documents found'
            });
        }
        const combinedContent = documents
            .map(doc => `${doc.filename}:\n${doc.content_text}`)
            .join('\n\n---\n\n');
        if (combinedContent.trim().length < 100) {
            return res.status(400).json({
                success: false,
                error: 'Document content is too short for AI generation'
            });
        }
        // Deduct credits
        const creditResult = await creditService_1.creditService.deductCredits(userId, 'quiz_generation', count);
        if (!creditResult.success) {
            return res.status(402).json({
                success: false,
                error: creditResult.message
            });
        }
        // Generate quiz questions using AI
        const questions = await aiService_1.aiService.generateQuizQuestions(combinedContent, count, customPrompt);
        // Add questions to the existing study set
        await studySetService_1.studySetService.addQuizQuestionsToSet(studySetId, questions);
        res.json({
            success: true,
            data: {
                questions,
                creditsRemaining: creditResult.remainingCredits
            }
        });
    }
    catch (error) {
        console.error('Generate more quiz questions error:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to generate quiz questions'
        });
    }
};
exports.generateMoreQuizQuestions = generateMoreQuizQuestions;
const generateQuiz = async (req, res) => {
    try {
        const userId = req.user.id;
        const { documentIds, name, count = 10, customPrompt } = req.body;
        // Validate input
        if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Document IDs are required'
            });
        }
        if (!name || name.trim().length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Study set name is required'
            });
        }
        // Get documents and combine content
        const documents = await documentDbService_1.documentDbService.getDocumentsByIds(documentIds, userId);
        if (documents.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'No documents found'
            });
        }
        const combinedContent = documents
            .map(doc => `${doc.filename}:\n${doc.content_text}`)
            .join('\n\n---\n\n');
        if (combinedContent.trim().length < 100) {
            return res.status(400).json({
                success: false,
                error: 'Document content is too short for AI generation'
            });
        }
        // Deduct credits
        const creditResult = await creditService_1.creditService.deductCredits(userId, 'quiz_generation', { documentIds, count, customPrompt: !!customPrompt });
        if (!creditResult.success) {
            return res.status(402).json({
                success: false,
                error: creditResult.message
            });
        }
        try {
            // Generate quiz questions with AI
            const questions = await aiService_1.aiService.generateQuizQuestions(combinedContent, count, customPrompt);
            // Create study set
            const studySet = await studySetService_1.studySetService.createStudySet({
                user_id: userId,
                name: name.trim(),
                type: 'quiz',
                is_ai_generated: true,
                source_documents: documents.map(doc => ({ id: doc.id, filename: doc.filename })),
                custom_prompt: customPrompt || null
            });
            // Add questions to study set
            await studySetService_1.studySetService.addQuizQuestionsToSet(studySet.id, questions);
            res.status(201).json({
                success: true,
                data: {
                    studySet,
                    questions,
                    creditsRemaining: creditResult.remainingCredits
                },
                message: 'Quiz generated successfully'
            });
        }
        catch (aiError) {
            // Refund credits if AI generation fails
            await creditService_1.creditService.addCredits(userId, 1, 'refund_failed_generation', `quiz_generation_${Date.now()}`);
            throw aiError;
        }
    }
    catch (error) {
        console.error('Generate quiz error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to generate quiz'
        });
    }
};
exports.generateQuiz = generateQuiz;
const testAIConnection = async (_req, res) => {
    try {
        const isConnected = await aiService_1.aiService.testConnection();
        res.json({
            success: true,
            data: { connected: isConnected },
            message: isConnected ? 'AI service is connected' : 'AI service connection failed'
        });
    }
    catch (error) {
        console.error('AI connection test error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to test AI connection'
        });
    }
};
exports.testAIConnection = testAIConnection;
const getStudySets = async (req, res) => {
    try {
        const userId = req.user.id;
        const { limit = 50, offset = 0 } = req.query;
        const studySets = await studySetService_1.studySetService.getUserStudySets(userId, Number(limit), Number(offset));
        res.json({
            success: true,
            data: studySets,
            message: 'Study sets retrieved successfully'
        });
    }
    catch (error) {
        console.error('Get study sets error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to get study sets'
        });
    }
};
exports.getStudySets = getStudySets;
const getStudySetContent = async (req, res) => {
    try {
        const userId = req.user.id;
        const { studySetId } = req.params;
        const studySet = await studySetService_1.studySetService.getStudySetById(studySetId, userId);
        if (!studySet) {
            return res.status(404).json({
                success: false,
                error: 'Study set not found'
            });
        }
        let content;
        if (studySet.type === 'flashcards') {
            content = await studySetService_1.studySetService.getFlashcardsByStudySet(studySetId, userId);
        }
        else if (studySet.type === 'quiz') {
            content = await studySetService_1.studySetService.getQuizQuestionsByStudySet(studySetId, userId);
        }
        res.json({
            success: true,
            data: {
                studySet,
                content
            },
            message: 'Study set content retrieved successfully'
        });
    }
    catch (error) {
        console.error('Get study set content error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to get study set content'
        });
    }
};
exports.getStudySetContent = getStudySetContent;
//# sourceMappingURL=aiController.js.map