import { create } from 'zustand';
import { StudySet, Flashcard, QuizQuestion } from '../../../shared/types';

interface StudyAction {
  type: 'NEXT_ITEM' | 'PREVIOUS_ITEM' | 'TOGGLE_FLAG' | 'MARK_REVIEWED' | 'SUBMIT_ANSWER';
  payload: any;
  previousState: Partial<StudySession>;
  timestamp: number;
}

interface StudySession {
  studySetId: string;
  type: 'flashcards' | 'quiz';
  startTime: Date;
  currentIndex: number;
  totalItems: number;
  reviewedItems: number[];
  flaggedItems: string[];
  correctAnswers?: number; // For quizzes
  timeSpent: number; // in seconds
}

interface StudyState {
  currentSession: StudySession | null;
  studySetContent: {
    studySet?: StudySet;
    flashcards?: Flashcard[];
    questions?: QuizQuestion[];
  } | null;
  isLoading: boolean;
  error: string | null;

  // Undo/Redo functionality
  actionHistory: StudyAction[];
  currentActionIndex: number;
  canUndo: boolean;
  canRedo: boolean;

  // Actions
  startStudySession: (studySetId: string, type: 'flashcards' | 'quiz') => Promise<void>;
  endStudySession: () => void;
  nextItem: () => void;
  previousItem: () => void;
  goToItem: (index: number) => void;
  toggleFlag: (itemId: string) => void;
  markReviewed: (itemId: string) => void;
  submitQuizAnswer: (questionId: string, answer: string[], isCorrect: boolean) => void;
  updateTimeSpent: (seconds: number) => void;
  fetchStudySetContent: (studySetId: string) => Promise<void>;

  // Undo/Redo actions
  undo: () => void;
  redo: () => void;
  clearHistory: () => void;
  addToHistory: (action: StudyAction) => void;
}

export const useStudyStore = create<StudyState>((set, get) => ({
  currentSession: null,
  studySetContent: null,
  isLoading: false,
  error: null,

  // Undo/Redo state
  actionHistory: [],
  currentActionIndex: -1,
  canUndo: false,
  canRedo: false,

  fetchStudySetContent: async (studySetId: string, lazy = false) => {
    // If lazy loading and content already exists, don't refetch
    const { studySetContent } = get();
    if (lazy && studySetContent?.studySet?.id === studySetId) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      const token = localStorage.getItem('auth_token');

      const response = await fetch(`/api/study-sets/${studySetId}/content`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Failed to fetch study set content');
      }

      const result = await response.json();

      if (result.success) {
        set({
          studySetContent: {
            studySet: result.data.studySet,
            flashcards: result.data.studySet.type === 'flashcards' ? result.data.content : undefined,
            questions: result.data.studySet.type === 'quiz' ? result.data.content : undefined,
          },
          isLoading: false
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch study set content',
        isLoading: false
      });
      throw error;
    }
  },

  startStudySession: async (studySetId: string, type: 'flashcards' | 'quiz') => {
    const { studySetContent, fetchStudySetContent } = get();
    
    // Fetch content if not already loaded
    if (!studySetContent || studySetContent.studySet?.id !== studySetId) {
      await fetchStudySetContent(studySetId);
    }

    const content = get().studySetContent;
    if (!content) {
      throw new Error('Failed to load study set content');
    }

    const totalItems = type === 'flashcards' 
      ? content.flashcards?.length || 0
      : content.questions?.length || 0;

    if (totalItems === 0) {
      throw new Error('No study materials found in this set');
    }

    set({
      currentSession: {
        studySetId,
        type,
        startTime: new Date(),
        currentIndex: 0,
        totalItems,
        reviewedItems: [],
        flaggedItems: [],
        correctAnswers: type === 'quiz' ? 0 : undefined,
        timeSpent: 0
      }
    });
  },

  endStudySession: () => {
    set({ currentSession: null });
  },

  nextItem: () => {
    const { currentSession, addToHistory } = get();
    if (!currentSession) return;

    // Implement circular navigation: if at last item, wrap to first item
    const nextIndex = currentSession.currentIndex === currentSession.totalItems - 1
      ? 0
      : currentSession.currentIndex + 1;

    // Record action in history
    addToHistory({
      type: 'NEXT_ITEM',
      payload: { fromIndex: currentSession.currentIndex, toIndex: nextIndex },
      previousState: { currentIndex: currentSession.currentIndex },
      timestamp: Date.now()
    });

    set({
      currentSession: {
        ...currentSession,
        currentIndex: nextIndex
      }
    });
  },

  previousItem: () => {
    const { currentSession, addToHistory } = get();
    if (!currentSession) return;

    // Implement circular navigation: if at first item, wrap to last item
    const prevIndex = currentSession.currentIndex === 0
      ? currentSession.totalItems - 1
      : currentSession.currentIndex - 1;

    // Record action in history
    addToHistory({
      type: 'PREVIOUS_ITEM',
      payload: { fromIndex: currentSession.currentIndex, toIndex: prevIndex },
      previousState: { currentIndex: currentSession.currentIndex },
      timestamp: Date.now()
    });

    set({
      currentSession: {
        ...currentSession,
        currentIndex: prevIndex
      }
    });
  },

  goToItem: (index: number) => {
    const { currentSession } = get();
    if (!currentSession) return;

    const clampedIndex = Math.max(0, Math.min(index, currentSession.totalItems - 1));
    set({
      currentSession: {
        ...currentSession,
        currentIndex: clampedIndex
      }
    });
  },

  toggleFlag: (itemId: string) => {
    const { currentSession, addToHistory } = get();
    if (!currentSession) return;

    const wasFlagged = currentSession.flaggedItems.includes(itemId);
    const flaggedItems = wasFlagged
      ? currentSession.flaggedItems.filter(id => id !== itemId)
      : [...currentSession.flaggedItems, itemId];

    // Record action in history
    addToHistory({
      type: 'TOGGLE_FLAG',
      payload: { itemId, wasFlagged },
      previousState: { flaggedItems: currentSession.flaggedItems },
      timestamp: Date.now()
    });

    set({
      currentSession: {
        ...currentSession,
        flaggedItems
      }
    });
  },

  markReviewed: (_itemId: string) => {
    const { currentSession } = get();
    if (!currentSession) return;

    if (!currentSession.reviewedItems.includes(currentSession.currentIndex)) {
      set({
        currentSession: {
          ...currentSession,
          reviewedItems: [...currentSession.reviewedItems, currentSession.currentIndex]
        }
      });
    }
  },

  submitQuizAnswer: (questionId: string, _answer: string[], isCorrect: boolean) => {
    const { currentSession, markReviewed } = get();
    if (!currentSession || currentSession.type !== 'quiz') return;

    markReviewed(questionId);

    if (isCorrect) {
      set({
        currentSession: {
          ...currentSession,
          correctAnswers: (currentSession.correctAnswers || 0) + 1
        }
      });
    }
  },

  updateTimeSpent: (seconds: number) => {
    const { currentSession } = get();
    if (!currentSession) return;

    set({
      currentSession: {
        ...currentSession,
        timeSpent: currentSession.timeSpent + seconds
      }
    });
  },

  // Helper function to add action to history
  addToHistory: (action: StudyAction) => {
    const { actionHistory, currentActionIndex } = get();

    // Remove any actions after current index (when undoing then doing new action)
    const newHistory = actionHistory.slice(0, currentActionIndex + 1);
    newHistory.push(action);

    // Limit history to last 50 actions for performance
    const limitedHistory = newHistory.slice(-50);

    set({
      actionHistory: limitedHistory,
      currentActionIndex: limitedHistory.length - 1,
      canUndo: limitedHistory.length > 0,
      canRedo: false
    });
  },

  undo: () => {
    const { actionHistory, currentActionIndex, currentSession } = get();

    if (currentActionIndex < 0 || !currentSession) return;

    const action = actionHistory[currentActionIndex];

    // Restore previous state
    set({
      currentSession: {
        ...currentSession,
        ...action.previousState
      },
      currentActionIndex: currentActionIndex - 1,
      canUndo: currentActionIndex > 0,
      canRedo: true
    });
  },

  redo: () => {
    const { actionHistory, currentActionIndex, currentSession } = get();

    if (currentActionIndex >= actionHistory.length - 1 || !currentSession) return;

    const nextActionIndex = currentActionIndex + 1;
    const action = actionHistory[nextActionIndex];

    // Re-apply the action
    switch (action.type) {
      case 'NEXT_ITEM':
        get().nextItem();
        break;
      case 'PREVIOUS_ITEM':
        get().previousItem();
        break;
      case 'TOGGLE_FLAG':
        get().toggleFlag(action.payload.itemId);
        break;
      case 'MARK_REVIEWED':
        get().markReviewed(action.payload.itemId);
        break;
    }

    set({
      currentActionIndex: nextActionIndex,
      canUndo: true,
      canRedo: nextActionIndex < actionHistory.length - 1
    });
  },

  clearHistory: () => {
    set({
      actionHistory: [],
      currentActionIndex: -1,
      canUndo: false,
      canRedo: false
    });
  }
}));
