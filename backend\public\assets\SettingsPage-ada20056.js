import{r as l,b as P,j as e,m as A,o as R,p as $,q as H,s as E,t as b,I as g,B as c,v as B,w as I,x as D,y as T}from"./index-3e70e512.js";const L=[{id:"profile",label:"Profile",icon:R,description:"Manage your account information"},{id:"preferences",label:"Preferences",icon:$,description:"Customize your experience"},{id:"notifications",label:"Notifications",icon:H,description:"Control notification settings"},{id:"security",label:"Security",icon:E,description:"Password and security settings"},{id:"billing",label:"Billing",icon:b,description:"Manage subscription and payments"}],F=()=>{const[d,p]=l.useState("profile"),[x,o]=l.useState(!1),{user:a}=P(),[t,m]=l.useState({name:(a==null?void 0:a.name)||"",email:(a==null?void 0:a.email)||"",bio:""}),[r,i]=l.useState({theme:"dark",language:"en",studyReminders:!0,autoSave:!0}),[u,y]=l.useState({emailNotifications:!0,studyReminders:!0,weeklyProgress:!1,marketingEmails:!1}),j=async()=>{o(!0),setTimeout(()=>o(!1),1e3)},f=async()=>{o(!0),setTimeout(()=>o(!1),1e3)},h=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Profile Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(g,{label:"Full Name",value:t.name,onChange:s=>m({...t,name:s}),placeholder:"Enter your full name"}),e.jsx(g,{label:"Email Address",type:"email",value:t.email,onChange:s=>m({...t,email:s}),placeholder:"Enter your email",disabled:!0}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio (Optional)"}),e.jsx("textarea",{value:t.bio,onChange:s=>m({...t,bio:s.target.value}),placeholder:"Tell us about yourself...",rows:4,className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(c,{onClick:j,isLoading:x,children:"Save Profile"})})]})}),N=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"App Preferences"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Theme"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsxs("button",{onClick:()=>i({...r,theme:"dark"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${r.theme==="dark"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:[e.jsx(B,{className:"w-4 h-4"}),e.jsx("span",{children:"Dark"})]}),e.jsxs("button",{onClick:()=>i({...r,theme:"light"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${r.theme==="light"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,disabled:!0,children:[e.jsx(I,{className:"w-4 h-4"}),e.jsx("span",{children:"Light (Coming Soon)"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Language"}),e.jsxs("select",{value:r.language,onChange:s=>i({...r,language:s.target.value}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:"en",children:"English"}),e.jsx("option",{value:"es",disabled:!0,children:"Spanish (Coming Soon)"}),e.jsx("option",{value:"fr",disabled:!0,children:"French (Coming Soon)"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Study Reminders"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Get reminded to study regularly"})]}),e.jsx("button",{onClick:()=>i({...r,studyReminders:!r.studyReminders}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${r.studyReminders?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${r.studyReminders?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Auto-save"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Automatically save your progress"})]}),e.jsx("button",{onClick:()=>i({...r,autoSave:!r.autoSave}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${r.autoSave?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${r.autoSave?"translate-x-6":"translate-x-1"}`})})]})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(c,{onClick:f,isLoading:x,children:"Save Preferences"})})]})}),v=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Notification Settings"}),e.jsx("div",{className:"space-y-4",children:Object.entries(u).map(([s,n])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300 capitalize",children:s.replace(/([A-Z])/g," $1").trim()}),e.jsxs("p",{className:"text-xs text-gray-500",children:[s==="emailNotifications"&&"Receive important updates via email",s==="studyReminders"&&"Get reminded when it's time to study",s==="weeklyProgress"&&"Weekly summary of your study progress",s==="marketingEmails"&&"Product updates and tips"]})]}),e.jsx("button",{onClick:()=>y({...u,[s]:!n}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${n?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${n?"translate-x-6":"translate-x-1"}`})})]},s))})]})}),w=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Security Settings"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(D,{className:"w-5 h-5 text-primary-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Change Password"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Update your password to keep your account secure"}),e.jsx(c,{variant:"secondary",children:"Change Password"})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(T,{className:"w-5 h-5 text-red-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Delete Account"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Permanently delete your account and all associated data"}),e.jsx(c,{variant:"danger",children:"Delete Account"})]})]})]})}),S=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Billing & Subscription"}),e.jsx("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:e.jsxs("div",{className:"text-center",children:[e.jsx(b,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-white mb-2",children:"Free Plan"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"You're currently on the free plan with limited AI generations"}),e.jsx(c,{children:"Upgrade to Pro"})]})})]})}),k=()=>{switch(d){case"profile":return h();case"preferences":return N();case"notifications":return v();case"security":return w();case"billing":return S();default:return h()}};return e.jsx("div",{className:"min-h-screen bg-background-primary text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Settings"}),e.jsx("p",{className:"text-gray-400",children:"Manage your account and preferences"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsx("nav",{className:"space-y-2",children:L.map(s=>{const n=s.icon,C=d===s.id;return e.jsxs("button",{onClick:()=>p(s.id),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${C?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(n,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("span",{className:"font-medium block",children:s.label}),e.jsx("span",{className:"text-xs text-gray-500 block truncate",children:s.description})]})]},s.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(A.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:k()},d)})]})]})})};export{F as SettingsPage};
