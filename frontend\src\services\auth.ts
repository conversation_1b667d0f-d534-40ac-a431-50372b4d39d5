import { createClient } from "@supabase/supabase-js";
import { <PERSON>r<PERSON>ro<PERSON>le, AuthResult } from "../../../shared/types";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

class AuthService {
  // Helper methods for token storage
  private setToken(token: string, rememberMe: boolean = true): void {
    // Clear any existing tokens from both storages
    localStorage.removeItem("auth_token");
    sessionStorage.removeItem("auth_token");

    // Store in appropriate storage based on user preference
    if (rememberMe) {
      localStorage.setItem("auth_token", token);
    } else {
      sessionStorage.setItem("auth_token", token);
    }
  }

  private getToken(): string | null {
    // Check localStorage first, then sessionStorage
    return localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token");
  }

  private clearToken(): void {
    localStorage.removeItem("auth_token");
    sessionStorage.removeItem("auth_token");
  }

  async signUp(
    email: string,
    password: string,
    name?: string
  ): Promise<AuthResult> {
    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password, name }),
      });
      const result = await response.json();
      if (result.success && result.token) {
        // Default to persistent storage for signup
        this.setToken(result.token, true);
      }
      return result;
    } catch {
      return { success: false, error: "Network error during signup" };
    }
  }

  async signIn(email: string, password: string, rememberMe: boolean = true): Promise<AuthResult> {
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });
      const result = await response.json();
      if (result.success && result.token) {
        this.setToken(result.token, rememberMe);
      }
      return result;
    } catch {
      return { success: false, error: "Network error during login" };
    }
  }

  async signOut(): Promise<void> {
    try {
      const token = this.getToken();
      if (token) {
        await fetch("/api/auth/logout", {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
        });
      }
    } finally {
      this.clearToken();
    }
  }

  async getCurrentUser(): Promise<UserProfile | null> {
    try {
      const token = this.getToken();
      if (!token) return null;
      const response = await fetch("/api/auth/user", {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!response.ok) {
        if (response.status === 401) this.clearToken();
        return null;
      }
      const result = await response.json();
      return result.success ? result.data : null;
    } catch {
      return null;
    }
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }
}

export const authService = new AuthService();
