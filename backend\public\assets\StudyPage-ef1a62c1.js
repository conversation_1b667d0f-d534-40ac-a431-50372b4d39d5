import{r as x,j as e,u as Q,a as B,B as j,d as R}from"./index-3e70e512.js";import{u as L}from"./studyStore-16bb1dcf.js";const H=({message:i,priority:d="polite",clearAfter:t=3e3})=>{const[r,h]=x.useState("");return x.useEffect(()=>{if(i&&(h(i),t>0)){const y=setTimeout(()=>{h("")},t);return()=>clearTimeout(y)}},[i,t]),e.jsx("div",{"aria-live":d,"aria-atomic":"true",className:"sr-only",role:"status",children:r})},O=()=>{const[i,d]=x.useState(""),[t,r]=x.useState("polite");return{announce:(v,I="polite")=>{r(I),d(v)},AnnouncementComponent:()=>e.jsx(H,{message:i,priority:t})}},U=x.memo(()=>{var o;const i=Q(),{alert:d}=B(),{currentSession:t,studySetContent:r,nextItem:h,previousItem:y,toggleFlag:v,markReviewed:I,endStudySession:p,updateTimeSpent:w,undo:m,redo:k,canUndo:F,canRedo:C}=L(),[g,l]=x.useState(!1),[A,T]=x.useState(Date.now()),{announce:b,AnnouncementComponent:D}=O();if(x.useEffect(()=>{const u=setInterval(()=>{w(1)},1e3);return()=>clearInterval(u)},[w]),x.useEffect(()=>{l(!1),T(Date.now())},[t==null?void 0:t.currentIndex]),x.useEffect(()=>{const u=n=>{var z;if(!(n.target instanceof HTMLInputElement||n.target instanceof HTMLTextAreaElement||n.target instanceof HTMLSelectElement||((z=n.target)==null?void 0:z.contentEditable)==="true"))if(n.key==="ArrowLeft"){if(n.preventDefault(),t){y();const S=t.currentIndex===0?t.totalItems:t.currentIndex;b(`Card ${S} of ${t.totalItems}`)}}else if(n.key==="ArrowRight"){if(n.preventDefault(),t){h();const S=t.currentIndex===t.totalItems-1?1:t.currentIndex+2;b(`Card ${S} of ${t.totalItems}`)}}else if(n.key===" ")n.preventDefault(),l(!g);else if(n.key==="f"||n.key==="F"){if(n.preventDefault(),t&&(r!=null&&r.flashcards)){const S=r.flashcards[t.currentIndex];if(S){v(S.id);const P=t.flaggedItems.includes(S.id);b(P?"Card unflagged":"Card flagged for review")}}}else(n.ctrlKey||n.metaKey)&&n.key==="z"&&!n.shiftKey?(n.preventDefault(),F&&m()):(n.ctrlKey||n.metaKey)&&(n.key==="y"||n.key==="z"&&n.shiftKey)&&(n.preventDefault(),C&&k())};return document.addEventListener("keydown",u),()=>document.removeEventListener("keydown",u)},[t,r,g,F,C,y,h,v,m,k,b]),!t||!(r!=null&&r.flashcards))return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:"No flashcard session found"}),e.jsx(j,{onClick:()=>i("/dashboard"),variant:"secondary",children:"Back to Dashboard"})]})});const N=r.flashcards[t.currentIndex],_=(t.currentIndex+1)/t.totalItems*100,E=t.currentIndex===0,M=t.currentIndex===t.totalItems-1,$=t.flaggedItems.includes(N.id),q=()=>{g&&I(N.id),h();const u=M?1:t.currentIndex+2;b(`Card ${u} of ${t.totalItems}`)},s=()=>{y();const u=E?t.totalItems:t.currentIndex;b(`Card ${u} of ${t.totalItems}`)},c=()=>{v(N.id),b(`Card ${$?"unflagged":"flagged"}`)},a=()=>{l(!g),b(g?"Showing front of card":"Showing back of card")},f=async()=>{const u=Math.floor((Date.now()-A)/1e3),n=t.reviewedItems.length,z=t.flaggedItems.length;p(),await d({title:"Study Session Complete!",message:`Reviewed: ${n}/${t.totalItems} cards
Flagged: ${z} cards
Time spent: ${Math.floor(u/60)}m ${u%60}s`,variant:"success",confirmText:"Continue"}),i(`/study-sets/${t.studySetId}`)};return e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("button",{onClick:()=>i(`/study-sets/${t.studySetId}`),className:"text-gray-400 hover:text-white flex items-center",children:"← Back to Study Set"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-xl font-semibold text-white",children:(o=r.studySet)==null?void 0:o.name}),e.jsxs("p",{className:"text-sm text-gray-400",children:["Card ",t.currentIndex+1," of ",t.totalItems]})]}),e.jsx(j,{onClick:f,variant:"secondary",size:"sm",children:"Finish"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:`${_}%`}})}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[e.jsxs("span",{children:["Progress: ",Math.round(_),"%"]}),e.jsxs("span",{children:["Time: ",Math.floor(t.timeSpent/60),":",(t.timeSpent%60).toString().padStart(2,"0")]})]})]}),e.jsx("div",{className:"mb-8 flashcard-container",children:e.jsxs("div",{className:`
            relative w-full min-h-[24rem] max-h-[32rem] cursor-pointer transition-transform duration-500 transform-style-preserve-3d
            ${g?"rotate-y-180":""}
          `,onClick:a,role:"button",tabIndex:0,"aria-label":`Flashcard ${t.currentIndex+1} of ${t.totalItems}. ${g?"Showing back":"Showing front"}. Click or press space to flip.`,onKeyDown:u=>{(u.key===" "||u.key==="Enter")&&(u.preventDefault(),a())},children:[e.jsx("div",{className:`
            absolute inset-0 w-full h-full backface-hidden
            bg-background-secondary border border-gray-600 rounded-lg p-6 sm:p-8
            flex flex-col justify-center text-center overflow-y-auto
          `,children:e.jsxs("div",{className:"flex-1 flex flex-col justify-center min-h-0",children:[e.jsx("div",{className:"text-sm text-gray-400 mb-4 flex-shrink-0",children:"FRONT"}),e.jsx("div",{className:"text-lg sm:text-xl text-white leading-relaxed break-words overflow-y-auto flex-1 flex items-center justify-center",children:e.jsx("div",{className:"max-w-full",children:N.front})}),!g&&e.jsx("div",{className:"text-sm text-gray-500 mt-6 flex-shrink-0",children:"Click to reveal answer"})]})}),e.jsx("div",{className:`
            absolute inset-0 w-full h-full backface-hidden rotate-y-180
            bg-primary-500/10 border border-primary-500/30 rounded-lg p-6 sm:p-8
            flex flex-col justify-center text-center overflow-y-auto
          `,children:e.jsxs("div",{className:"flex-1 flex flex-col justify-center min-h-0",children:[e.jsx("div",{className:"text-sm text-primary-400 mb-4 flex-shrink-0",children:"BACK"}),e.jsx("div",{className:"text-lg sm:text-xl text-white leading-relaxed break-words overflow-y-auto flex-1 flex items-center justify-center",children:e.jsx("div",{className:"max-w-full",children:N.back})}),g&&e.jsx("div",{className:"text-sm text-gray-500 mt-6 flex-shrink-0",children:"Click to flip back"})]})})]})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(j,{onClick:s,variant:"secondary",children:"← Previous"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(j,{onClick:c,variant:$?"primary":"secondary",size:"sm",children:$?"🚩 Flagged":"🏳️ Flag"}),e.jsx(j,{onClick:()=>l(!g),variant:"secondary",children:g?"Show Front":"Show Back"})]}),e.jsx(j,{onClick:q,variant:"primary",children:"Next →"})]}),e.jsx("div",{className:"mt-8 text-center text-sm text-gray-500",children:e.jsx("p",{children:"Keyboard shortcuts: ← → (navigate) • Space (flip) • F (flag)"})}),e.jsx(D,{})]})}),G=()=>{var q;const i=Q(),{alert:d}=B(),{currentSession:t,studySetContent:r,nextItem:h,submitQuizAnswer:y,endStudySession:v,updateTimeSpent:I}=L(),[p,w]=x.useState([]),[m,k]=x.useState(!1),[F,C]=x.useState(!1);if(x.useEffect(()=>{const s=setInterval(()=>{I(1)},1e3);return()=>clearInterval(s)},[I]),x.useEffect(()=>{w([]),k(!1),C(!1)},[t==null?void 0:t.currentIndex]),!t||!(r!=null&&r.questions))return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:"No quiz session found"}),e.jsx(j,{onClick:()=>i("/dashboard"),variant:"secondary",children:"Back to Dashboard"})]})});const l=r.questions[t.currentIndex],A=(t.currentIndex+1)/t.totalItems*100,T=t.currentIndex===t.totalItems-1,b=s=>{m||(l.question_type==="multiple_choice"||l.question_type==="true_false"?w([s]):l.question_type==="select_all"&&w(c=>c.includes(s)?c.filter(a=>a!==s):[...c,s]))},D=s=>{m||w([s])},K=()=>{if(m||p.length===0)return;const s=N();y(l.id,p,s),k(!0),C(!0)},N=()=>{var c;const s=l.correct_answers;if(l.question_type==="short_answer"){const a=((c=p[0])==null?void 0:c.toLowerCase().trim())||"";return s.some(f=>a.includes(f.toLowerCase().trim())||f.toLowerCase().trim().includes(a))}else return p.length===s.length&&p.every(a=>s.includes(a))},_=()=>{T?E():h()},E=async()=>{const s=t.totalItems,c=t.correctAnswers||0,a=Math.round(c/s*100),f=t.timeSpent;v(),await d({title:"Quiz Complete!",message:`Score: ${c}/${s} (${a}%)
Time spent: ${Math.floor(f/60)}m ${f%60}s`,variant:"success",confirmText:"Continue"}),i(`/study-sets/${t.studySetId}`)},M=()=>l.options?l.options.map((s,c)=>{const a=p.includes(s),f=l.correct_answers.includes(s);let o="w-full text-left p-4 rounded-lg border transition-all ";return m?f?o+="border-green-500 bg-green-500/20 text-green-400":a&&!f?o+="border-red-500 bg-red-500/20 text-red-400":o+="border-gray-600 bg-background-secondary text-gray-400":a?o+="border-primary-500 bg-primary-500/20 text-primary-400":o+="border-gray-600 bg-background-secondary text-white hover:border-gray-500",e.jsx("button",{onClick:()=>b(s),disabled:m,className:o,children:e.jsxs("div",{className:"flex items-center space-x-3",children:[l.question_type==="multiple_choice"?e.jsx("div",{className:`
                w-5 h-5 rounded-full border-2 flex items-center justify-center
                ${a?"border-current":"border-gray-500"}
              `,children:a&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-current"})}):e.jsx("div",{className:`
                w-5 h-5 rounded border-2 flex items-center justify-center
                ${a?"border-current":"border-gray-500"}
              `,children:a&&e.jsx("div",{className:"w-2 h-2 rounded bg-current"})}),e.jsx("span",{children:s})]})},c)}):null,$=()=>["True","False"].map(c=>{const a=p.includes(c),f=l.correct_answers.includes(c);let o="w-full text-left p-4 rounded-lg border transition-all ";return m?f?o+="border-green-500 bg-green-500/20 text-green-400":a&&!f?o+="border-red-500 bg-red-500/20 text-red-400":o+="border-gray-600 bg-background-secondary text-gray-400":a?o+="border-primary-500 bg-primary-500/20 text-primary-400":o+="border-gray-600 bg-background-secondary text-white hover:border-gray-500",e.jsx("button",{onClick:()=>b(c),disabled:m,className:o,children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`
              w-5 h-5 rounded-full border-2 flex items-center justify-center
              ${a?"border-current":"border-gray-500"}
            `,children:a&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-current"})}),e.jsx("span",{className:"text-lg font-medium",children:c})]})},c)});return e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("button",{onClick:()=>i(`/study-sets/${t.studySetId}`),className:"text-gray-400 hover:text-white flex items-center",children:"← Back to Study Set"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-xl font-semibold text-white",children:(q=r.studySet)==null?void 0:q.name}),e.jsxs("p",{className:"text-sm text-gray-400",children:["Question ",t.currentIndex+1," of ",t.totalItems]})]}),e.jsx(j,{onClick:E,variant:"secondary",size:"sm",children:"Finish Quiz"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:`${A}%`}})}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[e.jsxs("span",{children:["Progress: ",Math.round(A),"%"]}),e.jsxs("span",{children:["Score: ",t.correctAnswers||0,"/",t.currentIndex+(m?1:0)]}),e.jsxs("span",{children:["Time: ",Math.floor(t.timeSpent/60),":",(t.timeSpent%60).toString().padStart(2,"0")]})]})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsx("div",{className:"mb-4",children:e.jsx("span",{className:"text-sm text-gray-400 uppercase tracking-wide",children:l.question_type.replace("_"," ")})}),e.jsx("h2",{className:"text-xl text-white mb-6 leading-relaxed",children:l.question_text}),e.jsx("div",{className:"space-y-3",children:l.question_type==="short_answer"?e.jsx("textarea",{value:p[0]||"",onChange:s=>D(s.target.value),disabled:m,placeholder:"Type your answer here...",rows:3,className:"w-full px-4 py-3 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"}):l.question_type==="true_false"?$():M()}),!m&&e.jsx("div",{className:"mt-6",children:e.jsx(j,{onClick:K,disabled:p.length===0,className:"w-full",children:"Submit Answer"})}),m&&F&&l.explanation&&e.jsxs("div",{className:"mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg",children:[e.jsx("h4",{className:"text-blue-400 font-medium mb-2",children:"Explanation"}),e.jsx("p",{className:"text-gray-300",children:l.explanation})]})]}),m&&e.jsx("div",{className:"flex justify-center",children:e.jsx(j,{onClick:_,variant:"primary",size:"lg",children:T?"Finish Quiz":"Next Question →"})})]})},W=()=>{const{id:i,mode:d}=R(),t=Q(),{currentSession:r,startStudySession:h}=L();return x.useEffect(()=>{(!r||r.studySetId!==i||r.type!==d)&&(i&&d&&(d==="flashcards"||d==="quiz")?h(i,d).catch(y=>{console.error("Failed to start study session:",y),alert(y.message||"Failed to start study session"),t(`/study-sets/${i}`)}):t("/dashboard"))},[i,d,r,h,t]),r?d==="flashcards"?e.jsx(U,{}):d==="quiz"?e.jsx(G,{}):(t("/dashboard"),null):e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),e.jsx("span",{className:"ml-3 text-gray-400",children:"Starting study session..."})]})})};export{W as StudyPage};
