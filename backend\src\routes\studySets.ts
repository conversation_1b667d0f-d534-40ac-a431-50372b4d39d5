import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getStudySets,
  getStudySet,
  updateStudySet,
  deleteStudySet,
  getStudySetContent,
  updateStudyProgress
} from '../controllers/studySetController';

const router = Router();

// All study set routes require authentication
router.use(authenticateToken);

// Study set CRUD operations
router.get('/', getStudySets);
router.get('/:id', getStudySet);
router.put('/:id', updateStudySet);
router.delete('/:id', deleteStudySet);

// Study set content and progress
router.get('/:id/content', getStudySetContent);
router.post('/:id/study', updateStudyProgress);

export default router;
