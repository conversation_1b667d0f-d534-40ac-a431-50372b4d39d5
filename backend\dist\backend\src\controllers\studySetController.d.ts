import { Request, Response } from 'express';
export declare const getStudySets: (req: Request, res: Response) => Promise<void>;
export declare const getStudySet: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateStudySet: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteStudySet: (req: Request, res: Response) => Promise<void>;
export declare const getStudySetContent: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateStudyProgress: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=studySetController.d.ts.map