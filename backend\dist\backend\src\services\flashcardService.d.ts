import { Flashcard } from '../../../shared/types';
type FlashcardData = Omit<Flashcard, 'id' | 'study_set_id' | 'is_flagged' | 'times_reviewed' | 'last_reviewed_at'>;
export declare class FlashcardService {
    getFlashcardsByStudySet(studySetId: string, userId: string): Promise<Flashcard[]>;
    createFlashcard(studySetId: string, userId: string, flashcardData: FlashcardData): Promise<Flashcard>;
    updateFlashcard(flashcardId: string, userId: string, updates: Partial<FlashcardData>): Promise<Flashcard>;
    deleteFlashcard(flashcardId: string, userId: string): Promise<void>;
    updateFlashcardProgress(flashcardId: string, _userId: string): Promise<Flashcard>;
    toggleFlashcardFlag(flashcardId: string, userId: string): Promise<Flashcard>;
}
export declare const flashcardService: FlashcardService;
export {};
//# sourceMappingURL=flashcardService.d.ts.map