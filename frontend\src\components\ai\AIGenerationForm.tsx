import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAIStore } from '../../stores/aiStore';
import { useAuthStore } from '../../stores/authStore';
import { useDocumentStore } from '../../stores/documentStore';
import { useDialog } from '../../contexts/DialogContext';
import { DocumentSelector } from './DocumentSelector';
import { CompactUpload } from './CompactUpload';
import { Button } from '../common/Button';
import { Input } from '../common/Input';

interface AIGenerationFormProps {
  type: 'flashcards' | 'quiz';
}

export const AIGenerationForm: React.FC<AIGenerationFormProps> = ({ type }) => {
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [studySetName, setStudySetName] = useState('');
  const [itemCount, setItemCount] = useState(10);
  const [customPrompt, setCustomPrompt] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { generateFlashcards, generateQuiz, isGenerating, generationProgress } = useAIStore();
  const { user, updateUser } = useAuthStore();
  const { fetchDocuments } = useDocumentStore();
  const { alert } = useDialog();
  const navigate = useNavigate();

  // Handle upload completion - refresh document list
  const handleUploadComplete = () => {
    fetchDocuments();
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (selectedDocuments.length === 0) {
      newErrors.documents = 'Please select at least one document';
    }

    if (!studySetName.trim()) {
      newErrors.name = 'Study set name is required';
    }

    if (itemCount < 1 || itemCount > 50) {
      newErrors.count = 'Item count must be between 1 and 50';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    // Check credits
    if (!user || user.credits_remaining < 1) {
      await alert({
        title: 'Insufficient Credits',
        message: 'Please purchase more credits to continue.',
        variant: 'warning'
      });
      return;
    }

    try {
      const params = {
        documentIds: selectedDocuments,
        name: studySetName.trim(),
        count: itemCount,
        customPrompt: customPrompt.trim() || undefined
      };

      let result;
      if (type === 'flashcards') {
        result = await generateFlashcards(params);
      } else {
        result = await generateQuiz(params);
      }

      // Update user credits
      updateUser({ credits_remaining: result.creditsRemaining });

      // Navigate to the created study set
      navigate(`/study-sets/${result.studySet.id}`);
    } catch (error: any) {
      console.error('Generation error:', error);
      await alert({
        title: 'Generation Error',
        message: error.message || 'Failed to generate study materials. Please try again.',
        variant: 'error'
      });
    }
  };

  const creditCost = 1; // Each generation costs 1 credit
  const hasEnoughCredits = user && user.credits_remaining >= creditCost;

  return (
    <div className="max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-2">
            Generate {type === 'flashcards' ? 'Flashcards' : 'Quiz Questions'}
          </h2>
          <p className="text-gray-400">
            Create AI-powered study materials from your documents
          </p>
        </div>

        {/* Credit Info */}
        <div className="bg-background-secondary rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-300">
                Cost: <span className="font-medium text-primary-400">{creditCost} credit</span>
              </p>
              <p className="text-sm text-gray-400">
                Your balance: {user?.credits_remaining || 0} credits
              </p>
            </div>
            {!hasEnoughCredits && (
              <Button
                onClick={() => navigate('/credits')}
                variant="secondary"
                size="sm"
              >
                Buy Credits
              </Button>
            )}
          </div>
        </div>

        {/* Document Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Upload New Documents
          </label>
          <CompactUpload onUploadComplete={handleUploadComplete} />
        </div>

        {/* Document Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Select Documents *
          </label>
          <div className="bg-background-secondary rounded-lg p-4">
            <DocumentSelector
              selectedDocuments={selectedDocuments}
              onSelectionChange={setSelectedDocuments}
              maxSelection={5}
            />
          </div>
          {errors.documents && (
            <p className="mt-1 text-sm text-red-500">{errors.documents}</p>
          )}
        </div>

        {/* Study Set Name */}
        <Input
          label="Study Set Name"
          value={studySetName}
          onChange={setStudySetName}
          placeholder={`My ${type === 'flashcards' ? 'Flashcards' : 'Quiz'}`}
          error={errors.name}
          required
        />

        {/* Item Count */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Number of {type === 'flashcards' ? 'Flashcards' : 'Questions'}
          </label>
          <input
            type="number"
            min="1"
            max="50"
            value={itemCount}
            onChange={(e) => setItemCount(parseInt(e.target.value) || 10)}
            className="w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
          {errors.count && (
            <p className="mt-1 text-sm text-red-500">{errors.count}</p>
          )}
        </div>

        {/* Custom Prompt */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Custom Instructions (Optional)
          </label>
          <textarea
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            placeholder="Add specific instructions for the AI (e.g., focus on key concepts, include examples, etc.)"
            rows={3}
            className="w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>

        {/* Generation Progress */}
        {isGenerating && (
          <div className="bg-primary-500/10 border border-primary-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"></div>
              <span className="text-primary-400">{generationProgress}</span>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          isLoading={isGenerating}
          disabled={!hasEnoughCredits}
          className="w-full"
          size="lg"
        >
          {isGenerating 
            ? 'Generating...' 
            : `Generate ${type === 'flashcards' ? 'Flashcards' : 'Quiz'} (${creditCost} credit)`
          }
        </Button>

        {!hasEnoughCredits && (
          <p className="text-center text-sm text-red-400">
            Insufficient credits. Please purchase more credits to continue.
          </p>
        )}
      </form>
    </div>
  );
};
