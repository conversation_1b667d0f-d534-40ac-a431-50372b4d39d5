import{c as k,r as u,b as z,a as A,u as E,j as e,B as b,I as q}from"./index-3e70e512.js";import{D as F}from"./DocumentSelector-d6e5f501.js";const R=k(s=>({isGenerating:!1,generationProgress:"",lastGenerated:null,generateFlashcards:async o=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const n=localStorage.getItem("auth_token");s({generationProgress:"Generating flashcards with AI..."});const r=await fetch("/api/ai/generate-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`},body:JSON.stringify(o)});if(!r.ok){const i=await r.json();throw new Error(i.error||"Generation failed")}const t=await r.json();if(t.success)return s({lastGenerated:{studySet:t.data.studySet,content:t.data.flashcards,type:"flashcards"},isGenerating:!1,generationProgress:""}),{studySet:t.data.studySet,flashcards:t.data.flashcards,creditsRemaining:t.data.creditsRemaining};throw new Error(t.error)}catch(n){throw s({isGenerating:!1,generationProgress:""}),n}},generateQuiz:async o=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const n=localStorage.getItem("auth_token");s({generationProgress:"Generating quiz questions with AI..."});const r=await fetch("/api/ai/generate-quiz",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`},body:JSON.stringify(o)});if(!r.ok){const i=await r.json();throw new Error(i.error||"Generation failed")}const t=await r.json();if(t.success)return s({lastGenerated:{studySet:t.data.studySet,content:t.data.questions,type:"quiz"},isGenerating:!1,generationProgress:""}),{studySet:t.data.studySet,questions:t.data.questions,creditsRemaining:t.data.creditsRemaining};throw new Error(t.error)}catch(n){throw s({isGenerating:!1,generationProgress:""}),n}},clearLastGenerated:()=>{s({lastGenerated:null})}})),O=({type:s})=>{const[o,n]=u.useState([]),[r,t]=u.useState(""),[i,j]=u.useState(10),[f,w]=u.useState(""),[d,S]=u.useState({}),{generateFlashcards:N,generateQuiz:v,isGenerating:g,generationProgress:G}=R(),{user:c,updateUser:P}=z(),{alert:p}=A(),y=E(),C=()=>{const a={};return o.length===0&&(a.documents="Please select at least one document"),r.trim()||(a.name="Study set name is required"),(i<1||i>50)&&(a.count="Item count must be between 1 and 50"),S(a),Object.keys(a).length===0},I=async a=>{if(a.preventDefault(),!!C()){if(!c||c.credits_remaining<1){await p({title:"Insufficient Credits",message:"Please purchase more credits to continue.",variant:"warning"});return}try{const l={documentIds:o,name:r.trim(),count:i,customPrompt:f.trim()||void 0};let m;s==="flashcards"?m=await N(l):m=await v(l),P({credits_remaining:m.creditsRemaining}),y(`/study-sets/${m.studySet.id}`)}catch(l){console.error("Generation error:",l),await p({title:"Generation Error",message:l.message||"Failed to generate study materials. Please try again.",variant:"error"})}}},h=1,x=c&&c.credits_remaining>=h;return e.jsx("div",{className:"max-w-2xl mx-auto",children:e.jsxs("form",{onSubmit:I,className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("h2",{className:"text-2xl font-bold text-white mb-2",children:["Generate ",s==="flashcards"?"Flashcards":"Quiz Questions"]}),e.jsx("p",{className:"text-gray-400",children:"Create AI-powered study materials from your documents"})]}),e.jsx("div",{className:"bg-background-secondary rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("p",{className:"text-sm text-gray-300",children:["Cost: ",e.jsxs("span",{className:"font-medium text-primary-400",children:[h," credit"]})]}),e.jsxs("p",{className:"text-sm text-gray-400",children:["Your balance: ",(c==null?void 0:c.credits_remaining)||0," credits"]})]}),!x&&e.jsx(b,{onClick:()=>y("/credits"),variant:"secondary",size:"sm",children:"Buy Credits"})]})}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents *"}),e.jsx("div",{className:"bg-background-secondary rounded-lg p-4",children:e.jsx(F,{selectedDocuments:o,onSelectionChange:n,maxSelection:5})}),d.documents&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:d.documents})]}),e.jsx(q,{label:"Study Set Name",value:r,onChange:t,placeholder:`My ${s==="flashcards"?"Flashcards":"Quiz"}`,error:d.name,required:!0}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:["Number of ",s==="flashcards"?"Flashcards":"Questions"]}),e.jsx("input",{type:"number",min:"1",max:"50",value:i,onChange:a=>j(parseInt(a.target.value)||10),className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary-500"}),d.count&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:d.count})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:f,onChange:a=>w(a.target.value),placeholder:"Add specific instructions for the AI (e.g., focus on key concepts, include examples, etc.)",rows:3,className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),g&&e.jsx("div",{className:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"}),e.jsx("span",{className:"text-primary-400",children:G})]})}),e.jsx(b,{type:"submit",isLoading:g,disabled:!x,className:"w-full",size:"lg",children:g?"Generating...":`Generate ${s==="flashcards"?"Flashcards":"Quiz"} (${h} credit)`}),!x&&e.jsx("p",{className:"text-center text-sm text-red-400",children:"Insufficient credits. Please purchase more credits to continue."})]})})};export{O as A};
