"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.flashcardService = exports.FlashcardService = void 0;
const supabaseService_1 = require("./supabaseService");
class FlashcardService {
    async getFlashcardsByStudySet(studySetId, userId) {
        // Verify user owns the study set
        const { data: studySet, error: studySetError } = await supabaseService_1.supabase
            .from('study_sets')
            .select('id')
            .eq('id', studySetId)
            .eq('user_id', userId)
            .single();
        if (studySetError || !studySet) {
            throw new Error('Study set not found or access denied');
        }
        const { data, error } = await supabaseService_1.supabase
            .from('flashcards')
            .select('*')
            .eq('study_set_id', studySetId)
            .order('created_at', { ascending: true });
        if (error) {
            throw new Error(`Failed to get flashcards: ${error.message}`);
        }
        return data || [];
    }
    async createFlashcard(studySetId, userId, flashcardData) {
        // Verify user owns the study set
        const { data: studySet, error: studySetError } = await supabaseService_1.supabase
            .from('study_sets')
            .select('id')
            .eq('id', studySetId)
            .eq('user_id', userId)
            .single();
        if (studySetError || !studySet) {
            throw new Error('Study set not found or access denied');
        }
        const { data, error } = await supabaseService_1.supabase
            .from('flashcards')
            .insert({
            study_set_id: studySetId,
            front: flashcardData.front,
            back: flashcardData.back,
            difficulty_level: flashcardData.difficulty_level || 3,
            is_ai_generated: flashcardData.is_ai_generated || false
        })
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create flashcard: ${error.message}`);
        }
        return data;
    }
    async updateFlashcard(flashcardId, userId, updates) {
        // Verify user owns the flashcard through study set
        const { data: flashcard, error: flashcardError } = await supabaseService_1.supabase
            .from('flashcards')
            .select('study_set_id')
            .eq('id', flashcardId)
            .single();
        if (flashcardError || !flashcard) {
            throw new Error('Flashcard not found');
        }
        const { data: studySet, error: studySetError } = await supabaseService_1.supabase
            .from('study_sets')
            .select('id')
            .eq('id', flashcard.study_set_id)
            .eq('user_id', userId)
            .single();
        if (studySetError || !studySet) {
            throw new Error('Access denied');
        }
        const { data, error } = await supabaseService_1.supabase
            .from('flashcards')
            .update(updates)
            .eq('id', flashcardId)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to update flashcard: ${error.message}`);
        }
        return data;
    }
    async deleteFlashcard(flashcardId, userId) {
        // Verify user owns the flashcard through study set
        const { data: flashcard, error: flashcardError } = await supabaseService_1.supabase
            .from('flashcards')
            .select('study_set_id')
            .eq('id', flashcardId)
            .single();
        if (flashcardError || !flashcard) {
            throw new Error('Flashcard not found');
        }
        const { data: studySet, error: studySetError } = await supabaseService_1.supabase
            .from('study_sets')
            .select('id')
            .eq('id', flashcard.study_set_id)
            .eq('user_id', userId)
            .single();
        if (studySetError || !studySet) {
            throw new Error('Access denied');
        }
        const { error } = await supabaseService_1.supabase
            .from('flashcards')
            .delete()
            .eq('id', flashcardId);
        if (error) {
            throw new Error(`Failed to delete flashcard: ${error.message}`);
        }
    }
    async updateFlashcardProgress(flashcardId, _userId) {
        // First get current review count
        const { data: current, error: getCurrentError } = await supabaseService_1.supabase
            .from('flashcards')
            .select('times_reviewed')
            .eq('id', flashcardId)
            .single();
        if (getCurrentError || !current) {
            throw new Error('Flashcard not found');
        }
        const { data, error } = await supabaseService_1.supabase
            .from('flashcards')
            .update({
            times_reviewed: current.times_reviewed + 1,
            last_reviewed_at: new Date().toISOString()
        })
            .eq('id', flashcardId)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to update flashcard progress: ${error.message}`);
        }
        return data;
    }
    async toggleFlashcardFlag(flashcardId, userId) {
        // Get current flag status
        const { data: current, error: getCurrentError } = await supabaseService_1.supabase
            .from('flashcards')
            .select('is_flagged, study_set_id')
            .eq('id', flashcardId)
            .single();
        if (getCurrentError || !current) {
            throw new Error('Flashcard not found');
        }
        // Verify ownership
        const { data: studySet, error: studySetError } = await supabaseService_1.supabase
            .from('study_sets')
            .select('id')
            .eq('id', current.study_set_id)
            .eq('user_id', userId)
            .single();
        if (studySetError || !studySet) {
            throw new Error('Access denied');
        }
        const { data, error } = await supabaseService_1.supabase
            .from('flashcards')
            .update({ is_flagged: !current.is_flagged })
            .eq('id', flashcardId)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to toggle flashcard flag: ${error.message}`);
        }
        return data;
    }
}
exports.FlashcardService = FlashcardService;
exports.flashcardService = new FlashcardService();
//# sourceMappingURL=flashcardService.js.map