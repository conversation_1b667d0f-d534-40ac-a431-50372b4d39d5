import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getFlashcards,
  createFlashcard,
  updateFlashcard,
  deleteFlashcard,
  toggleFlashcardFlag
} from '../controllers/flashcardController';

const router = Router();

// All flashcard routes require authentication
router.use(authenticateToken);

// Flashcard CRUD operations
router.get('/study-set/:studySetId', getFlashcards);
router.post('/study-set/:studySetId', createFlashcard);
router.put('/:id', updateFlashcard);
router.delete('/:id', deleteFlashcard);
router.patch('/:id/flag', toggleFlashcardFlag);

export default router;
