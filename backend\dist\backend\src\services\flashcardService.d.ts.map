{"version": 3, "file": "flashcardService.d.ts", "sourceRoot": "", "sources": ["../../../../src/services/flashcardService.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAElD,KAAK,aAAa,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,cAAc,GAAG,YAAY,GAAG,gBAAgB,GAAG,kBAAkB,CAAC,CAAC;AAEnH,qBAAa,gBAAgB;IACrB,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;IA0BjF,eAAe,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC;IAgCrG,eAAe,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC;IAqCzG,eAAe,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAiCnE,uBAAuB,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC;IA6BjF,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC;CAqCnF;AAED,eAAO,MAAM,gBAAgB,kBAAyB,CAAC"}