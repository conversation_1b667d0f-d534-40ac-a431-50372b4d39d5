import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface UseKeyboardShortcutsOptions {
  enableGlobalShortcuts?: boolean;
  enableNavigationShortcuts?: boolean;
}

export const useKeyboardShortcuts = (options: UseKeyboardShortcutsOptions = {}) => {
  const {
    enableGlobalShortcuts = true,
    enableNavigationShortcuts = true
  } = options;

  const navigate = useNavigate();
  const [showShortcutsModal, setShowShortcutsModal] = useState(false);
  const [lastKeySequence, setLastKeySequence] = useState<string[]>([]);

  useEffect(() => {
    if (!enableGlobalShortcuts) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        e.target instanceof HTMLSelectElement ||
        (e.target as HTMLElement)?.contentEditable === 'true'
      ) {
        return;
      }

      // Handle single key shortcuts
      if (e.key === '?' && !e.ctrlKey && !e.metaKey && !e.altKey) {
        e.preventDefault();
        setShowShortcutsModal(true);
        return;
      }

      if (e.key === 'Escape') {
        setShowShortcutsModal(false);
        setLastKeySequence([]);
        return;
      }

      // Handle navigation shortcuts (G + key combinations)
      if (enableNavigationShortcuts) {
        if (e.key.toLowerCase() === 'g' && !e.ctrlKey && !e.metaKey && !e.altKey) {
          e.preventDefault();
          setLastKeySequence(['g']);
          
          // Clear sequence after 2 seconds
          setTimeout(() => setLastKeySequence([]), 2000);
          return;
        }

        // Handle second key in G + key sequence
        if (lastKeySequence.includes('g')) {
          e.preventDefault();
          
          switch (e.key.toLowerCase()) {
            case 'd':
              navigate('/dashboard');
              break;
            case 'o':
              navigate('/documents');
              break;
            case 's':
              // Navigate to study sets (could be dashboard with focus on study sets)
              navigate('/dashboard');
              break;
            case 'a':
              navigate('/analytics');
              break;
            default:
              // Invalid sequence, clear it
              break;
          }
          
          setLastKeySequence([]);
          return;
        }
      }

      // Handle document shortcuts
      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'u':
            if (window.location.pathname === '/documents') {
              e.preventDefault();
              // Trigger upload - could emit custom event or use context
              const uploadEvent = new CustomEvent('trigger-upload');
              document.dispatchEvent(uploadEvent);
            }
            break;
          case 'f':
            if (window.location.pathname === '/documents') {
              e.preventDefault();
              // Focus search input
              const searchInput = document.querySelector('input[placeholder*="search" i]') as HTMLInputElement;
              if (searchInput) {
                searchInput.focus();
              }
            }
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [enableGlobalShortcuts, enableNavigationShortcuts, navigate, lastKeySequence]);

  return {
    showShortcutsModal,
    setShowShortcutsModal,
    lastKeySequence
  };
};
