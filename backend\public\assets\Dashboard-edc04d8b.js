import{r as t,u as g,j as e,B as r}from"./index-3e70e512.js";const p=t.memo(()=>{const a=g(),[c,x]=t.useState([]),[m,h]=t.useState(!0),[l,u]=t.useState(null);t.useEffect(()=>{d()},[]);const d=async()=>{try{const s=localStorage.getItem("auth_token"),i=await fetch("/api/study-sets",{headers:{Authorization:`Bearer ${s}`}});if(!i.ok)throw new Error("Failed to fetch study sets");const n=await i.json();if(n.success)x(n.data);else throw new Error(n.error||"Failed to fetch study sets")}catch(s){u(s.message)}finally{h(!1)}},o=s=>new Date(s).toLocaleDateString();return e.jsx("div",{className:"min-h-screen bg-background-primary text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-white",children:"Welcome to ChewyAI Dashboard"}),e.jsx("p",{className:"text-gray-400 mt-2",children:"Manage your study materials and track your progress"})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(r,{onClick:()=>a("/generate/flashcards"),variant:"primary",children:"Generate Flashcards"}),e.jsx(r,{onClick:()=>a("/generate/quiz"),variant:"primary",children:"Generate Quiz"})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Your Study Sets"}),m?e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),e.jsx("span",{className:"ml-3 text-gray-400",children:"Loading study sets..."})]}):l?e.jsxs("div",{className:"text-center py-12",children:[e.jsxs("div",{className:"text-red-400 mb-4",children:["Error: ",l]}),e.jsx(r,{onClick:d,variant:"secondary",children:"Try Again"})]}):c.length===0?e.jsxs("div",{className:"text-center py-12 bg-gray-800/50 rounded-lg",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:"No study sets found"}),e.jsx("p",{className:"text-gray-500 mb-6",children:"Create your first study set by generating flashcards or quizzes from your documents"}),e.jsxs("div",{className:"flex justify-center space-x-4",children:[e.jsx(r,{onClick:()=>a("/generate/flashcards"),variant:"primary",children:"Generate Flashcards"}),e.jsx(r,{onClick:()=>a("/generate/quiz"),variant:"secondary",children:"Generate Quiz"})]})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:c.map(s=>e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-6 hover:bg-gray-800/70 transition-colors cursor-pointer",onClick:()=>a(`/study-sets/${s.id}`),children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-white truncate",children:s.name}),e.jsx("span",{className:"text-xs bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:s.type})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-400",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Items:"}),e.jsx("span",{children:s.type==="flashcards"?s.flashcard_count||0:s.quiz_question_count||0})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Created:"}),e.jsx("span",{children:o(s.created_at)})]}),s.last_studied_at&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Last studied:"}),e.jsx("span",{children:o(s.last_studied_at)})]}),s.is_ai_generated&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{className:"text-xs",children:"🤖"}),e.jsx("span",{children:"AI Generated"})]})]}),e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-700",children:e.jsx("div",{onClick:i=>{i.stopPropagation(),a(`/study-sets/${s.id}`)},children:e.jsx(r,{variant:"secondary",size:"sm",className:"w-full",children:"Start Studying"})})})]},s.id))})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-6 hover:bg-gray-800/70 transition-colors cursor-pointer",onClick:()=>a("/documents"),children:[e.jsx("div",{className:"text-2xl mb-3",children:"📄"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Manage Documents"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Upload and organize your study materials"})]}),e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-6 hover:bg-gray-800/70 transition-colors cursor-pointer",onClick:()=>a("/generate/flashcards"),children:[e.jsx("div",{className:"text-2xl mb-3",children:"🃏"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Create Flashcards"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Generate flashcards from your documents"})]}),e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-6 hover:bg-gray-800/70 transition-colors cursor-pointer",onClick:()=>a("/generate/quiz"),children:[e.jsx("div",{className:"text-2xl mb-3",children:"❓"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Create Quiz"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Generate quiz questions from your documents"})]})]})]})})});export{p as Dashboard};
