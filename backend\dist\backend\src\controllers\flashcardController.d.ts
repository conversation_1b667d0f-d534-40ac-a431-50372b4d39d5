import { Request, Response } from 'express';
export declare const getFlashcards: (req: Request, res: Response) => Promise<void>;
export declare const createFlashcard: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateFlashcard: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteFlashcard: (req: Request, res: Response) => Promise<void>;
export declare const toggleFlashcardFlag: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=flashcardController.d.ts.map