import { Flashcard, QuizQuestion } from '../../../shared/types';
export declare class AIService {
    private readonly apiKey;
    private readonly baseUrl;
    private readonly model;
    constructor();
    generateFlashcards(content: string, count?: number, customPrompt?: string): Promise<Omit<Flashcard, 'id' | 'study_set_id' | 'is_flagged' | 'created_at' | 'updated_at'>[]>;
    generateQuizQuestions(content: string, count?: number, customPrompt?: string): Promise<Omit<QuizQuestion, 'id' | 'study_set_id' | 'created_at' | 'updated_at'>[]>;
    private buildFlashcardPrompt;
    private buildQuizPrompt;
    private callOpenRouter;
    private parseFlashcardResponse;
    private parseQuizResponse;
    private validateDifficultyLevel;
    testConnection(): Promise<boolean>;
}
export declare class AIError extends Error {
    originalError?: any | undefined;
    constructor(message: string, originalError?: any | undefined);
}
export declare const aiService: AIService;
//# sourceMappingURL=aiService.d.ts.map